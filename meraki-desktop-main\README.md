# Meraki Desktop Application

## Overview
This is the desktop companion application for the Meraki Organization Management System. It provides time tracking, productivity monitoring, and task management capabilities directly from your desktop.

## Features
- **Time Tracking**: Automatic check-in/check-out functionality
- **Productivity Monitoring**: Track active/idle time and productivity metrics
- **Task Management**: Manage and track tasks from the desktop
- **Screenshot Capture**: Automatic screenshot capture for productivity tracking
- **System Tray Integration**: Minimize to system tray for background operation
- **Keyboard Monitoring**: Track keyboard activity for productivity metrics

## Quick Start

### Method 1: Using the Startup Script (Recommended)
```bash
# Double-click start.bat or run from command line
start.bat
```

### Method 2: Using NPX
```bash
npx electron .
```

### Method 3: Direct Electron Execution
```bash
node_modules\.bin\electron.cmd .
```

## Configuration

### Environment Variables
Create a `.env` file in the root directory with the following variables:
```env
# Backend API URL
API_BASE_URL=http://localhost:10000/api

# Screenshot upload settings
SCREENSHOT_INTERVAL=300000  # 5 minutes in milliseconds
SCREENSHOT_QUALITY=80       # JPEG quality (1-100)

# Productivity tracking
IDLE_TIMEOUT=300000         # 5 minutes in milliseconds
PRODUCTIVITY_INTERVAL=60000 # 1 minute in milliseconds
```

## Application Structure

### Main Components
- **main.js**: Main Electron process and application logic
- **html_files/**: HTML templates for different windows
- **preload_files/**: Preload scripts for secure IPC communication
- **renderer_files/**: Renderer process scripts
- **styles/**: CSS stylesheets
- **assets/**: Application icons and images

### Key Windows
1. **Login Window**: User authentication
2. **Dashboard Window**: Main application interface
3. **Task Windows**: Task management and tracking
4. **Break Windows**: Break time management
5. **Settings Windows**: Application configuration

## Troubleshooting

### Common Issues

#### 1. Application Won't Start
**Error**: `electron-forge was terminated: You must depend on "electron-prebuilt-compile"`
**Solution**: Use the provided startup script or run `npx electron .` directly

#### 2. File Lock Errors During Installation
**Error**: `EBUSY: resource busy or locked`
**Solution**: 
1. Close all Electron processes: `taskkill /f /im electron.exe`
2. Wait a few seconds
3. Try installation again

#### 3. Sharp Module Issues
**Error**: Sharp module compilation errors
**Solution**: Run `npm run rebuild` to rebuild native modules

#### 4. Permission Issues
**Error**: Access denied or permission errors
**Solution**: Run PowerShell/Command Prompt as Administrator

### Dependency Issues
If you encounter dependency installation issues:

1. **Clean npm cache**:
   ```bash
   npm cache clean --force
   ```

2. **Remove node_modules** (if not locked):
   ```bash
   Remove-Item -Recurse -Force node_modules
   npm install
   ```

3. **Use alternative installation**:
   ```bash
   npm install --no-optional --force
   ```

## Development

### Running in Development Mode
```bash
npm run dev
```
This uses nodemon to watch for changes and restart the application automatically.

### Building for Production
```bash
npm run build
```

### Rebuilding Native Modules
```bash
npm run rebuild
```

## API Integration

The desktop application integrates with the Meraki backend API for:
- User authentication
- Activity tracking
- Task management
- Screenshot uploads
- Productivity data sync

### API Endpoints Used
- `POST /auth/login` - User authentication
- `GET /api/user/profile` - User profile data
- `POST /api/activity/checkin` - Check-in functionality
- `POST /api/activity/checkout` - Check-out functionality
- `POST /api/track/upload-screenshot` - Screenshot uploads
- `GET /api/product/ongoing-tasks` - Task data

## Security Features

- **Context Isolation**: Enabled for all renderer processes
- **Node Integration**: Disabled for security
- **Preload Scripts**: Secure IPC communication
- **Content Security Policy**: Prevents XSS attacks

## Performance Optimization

### Memory Management
- Proper window cleanup on close
- Timer cleanup to prevent memory leaks
- Efficient screenshot compression using Sharp

### CPU Optimization
- Configurable screenshot intervals
- Idle detection to reduce background activity
- Efficient keyboard monitoring

## System Requirements

- **OS**: Windows 10/11, macOS 10.14+, Linux (Ubuntu 18.04+)
- **RAM**: Minimum 4GB, Recommended 8GB
- **Storage**: 500MB free space
- **Network**: Internet connection for API communication

## Support

For issues and support:
1. Check the troubleshooting section above
2. Review the console output for error messages
3. Ensure the backend API is running and accessible
4. Verify network connectivity

## Version Information

- **Electron**: v22.3.27
- **Node.js**: Compatible with Electron's Node.js version
- **Platform**: Cross-platform (Windows, macOS, Linux)
