#!/usr/bin/env pwsh

Write-Host "Starting Meraki Desktop Application..." -ForegroundColor Green
Write-Host ""

# Kill any existing Electron processes
Write-Host "Stopping any existing Electron processes..." -ForegroundColor Yellow
try {
    Get-Process | Where-Object {$_.ProcessName -like "*electron*"} | Stop-Process -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2
} catch {
    Write-Host "No existing Electron processes found." -ForegroundColor Gray
}

# Check if node_modules exists
if (-not (Test-Path "node_modules")) {
    Write-Host "node_modules not found. Installing dependencies..." -ForegroundColor Yellow
    npm install --no-optional
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Warning: Some dependencies may not have installed correctly." -ForegroundColor Yellow
        Write-Host "The application may still work with existing dependencies." -ForegroundColor Yellow
    }
}

# Try to start the application
Write-Host "Launching Electron application..." -ForegroundColor Green

# Method 1: Try npx electron
try {
    npx electron .
} catch {
    Write-Host "npx method failed, trying alternative..." -ForegroundColor Yellow
    
    # Method 2: Try direct electron execution
    try {
        if (Test-Path "node_modules\.bin\electron.cmd") {
            & "node_modules\.bin\electron.cmd" .
        } elseif (Test-Path "node_modules\electron\dist\electron.exe") {
            & "node_modules\electron\dist\electron.exe" .
        } else {
            Write-Host "Electron executable not found. Please run 'npm install' first." -ForegroundColor Red
            exit 1
        }
    } catch {
        Write-Host "Failed to start application. Error: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

Write-Host "Application started successfully!" -ForegroundColor Green
