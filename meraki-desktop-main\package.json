{"name": "meraki-desktop", "version": "1.0.0", "main": "main.js", "scripts": {"start": "electron .", "dev": "nodemon --watch main.js --exec \"electron .\"", "electron": "electron .", "build": "electron-builder", "rebuild": "electron-rebuild -f -w sharp", "postinstall": "electron-rebuild"}, "devDependencies": {"electron": "^22.3.27", "electron-builder": "^24.13.3", "electron-rebuild": "^3.2.9", "nodemon": "^3.1.10"}, "dependencies": {"axios": "^1.9.0", "dotenv": "^16.5.0", "electron-squirrel-startup": "^1.0.1", "electron-store": "^8.2.0", "form-data": "^4.0.2", "node-global-key-listener": "^0.3.0", "sharp": "^0.34.1"}, "build": {"appId": "com.meraki.desktop", "productName": "<PERSON><PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["main.js", "html_files/**/*", "preload_files/**/*", "renderer_files/**/*", "styles/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/meraki-logo.png"}, "mac": {"target": "dmg", "icon": "assets/meraki-logo.png"}, "linux": {"target": "AppImage", "icon": "assets/meraki-logo.png"}}}