// Shared timer utility for synchronizing timer state across components
const TIMER_STORAGE_KEY = "globalTaskTimer";

// Global timer instance that runs independently of components
let globalTimerInterval = null;

// Get shared timer state
export const getGlobalTimerState = () => {
  try {
    const stored = sessionStorage.getItem(TIMER_STORAGE_KEY);
    return stored ? JSON.parse(stored) : {
      elapsedTime: 0,
      runningTask: null,
      isRunning: false
    };
  } catch (e) {
    return { elapsedTime: 0, runningTask: null, isRunning: false };
  }
};

// Set shared timer state
export const setGlobalTimerState = (state) => {
  try {
    sessionStorage.setItem(TIMER_STORAGE_KEY, JSON.stringify(state));
    // Dispatch custom event to notify other components
    window.dispatchEvent(new CustomEvent('timerStateChanged', { detail: state }));
  } catch (e) {
    console.error("Error storing timer state:", e);
  }
};

// Start global timer that runs independently of components
export const startGlobalTimer = (runningTask) => {
  if (globalTimerInterval) {
    clearInterval(globalTimerInterval);
  }

  if (!runningTask || runningTask.isPaused) {
    return;
  }

  // Calculate initial elapsed time
  const sessionStartTime = runningTask.sessionStartTime || runningTask.startTime;
  const currentTime = Date.now();
  let elapsedTime = Math.floor((currentTime - sessionStartTime) / 1000);

  // Update initial state
  setGlobalTimerState({ elapsedTime, runningTask, isRunning: true });

  // Start the interval
  globalTimerInterval = setInterval(() => {
    elapsedTime += 1;
    const currentState = getGlobalTimerState();

    // Only continue if task is still running and not paused
    if (currentState.runningTask && !currentState.runningTask.isPaused) {
      setGlobalTimerState({
        elapsedTime,
        runningTask: currentState.runningTask,
        isRunning: true
      });
    } else {
      stopGlobalTimer();
    }
  }, 1000);
};

// Stop global timer
export const stopGlobalTimer = () => {
  if (globalTimerInterval) {
    clearInterval(globalTimerInterval);
    globalTimerInterval = null;
  }

  const currentState = getGlobalTimerState();
  setGlobalTimerState({
    ...currentState,
    isRunning: false
  });
};

// Clear shared timer state
export const clearGlobalTimerState = () => {
  try {
    stopGlobalTimer();
    sessionStorage.removeItem(TIMER_STORAGE_KEY);
    window.dispatchEvent(new CustomEvent('timerStateChanged', { detail: null }));
  } catch (e) {
    console.error("Error clearing timer state:", e);
  }
};

// Format time from seconds to hh:mm:ss
export const formatTime = (seconds) => {
  const hrs = Math.floor(seconds / 3600);
  const mins = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  return `${hrs}h ${mins}m ${secs}s`;
};

// Convert decimal hours to hh:mm:ss
export const formatDecimalToTime = (decimalHours) => {
  if (!decimalHours) { return "0h 0m 0s" }
  return formatTime(Math.floor(decimalHours * 3600));
};