@echo off
echo Starting Meraki Desktop Application...
echo.

REM Kill any existing Electron processes
taskkill /f /im electron.exe >nul 2>&1

REM Wait a moment for processes to close
timeout /t 2 /nobreak >nul

REM Start the application
echo Launching Electron application...
npx electron .

REM If npx fails, try direct execution
if %errorlevel% neq 0 (
    echo Trying alternative startup method...
    node_modules\.bin\electron.cmd .
)

pause
